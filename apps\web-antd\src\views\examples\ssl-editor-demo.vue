<script setup lang="ts">
import type { SslCodeEditorInstance } from '#/components/ssl-code-editor';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { <PERSON><PERSON>, Card, Space, Switch } from 'ant-design-vue';

import { SslCodeEditor } from '#/components/ssl-code-editor';

// 示例SSL代码
const sampleCode = `/* SSL代码示例 - 样品处理流程 ;
:PROCEDURE ProcessSample
  :DECLARE sampleId, testResult, userName
  
  /* 获取当前用户信息 ;
  userName := MYUSERNAME
  
  /* 查询样品信息 ;
  sampleId := GetDataSet("SELECT SAMPLEID FROM SAMPLE WHERE STATUS = 'A' AND ASSIGNEDTO = '" + userName + "'")
  
  :IF !Empty(sampleId)
    InfoMes("处理样品: " + sampleId)
    
    /* 执行测试 ;
    testResult := DoProc("ExecuteTest", sampleId)
    
    :IF testResult = .t.
      /* 更新样品状态 ;
      SqlExecute("UPDATE SAMPLE SET STATUS = 'C', COMPLETEDDATE = '" + LimsDate() + "' WHERE SAMPLEID = '" + sampleId + "'")
      InfoMes("样品 " + sampleId + " 处理完成")
    :ELSE
      ErrorMes("样品 " + sampleId + " 处理失败")
    :ENDIF
  :ELSE
    InfoMes("没有找到待处理的样品")
  :ENDIF
:ENDPROC`;

const code = ref(sampleCode);
const readonly = ref(false);
const lineNumbers = ref(true);
const foldGutter = ref(true);
const searchEnabled = ref(true);
const theme = ref<'auto' | 'dark' | 'light'>('auto');
const editorRef = ref<SslCodeEditorInstance>();

// 编辑器事件处理
const handleChange = (value: string) => {
  console.log('代码内容变化:', value.length, '字符');
};

const handleReady = () => {
  console.log('编辑器初始化完成');
};

const handleFocus = () => {
  console.log('编辑器获得焦点');
};

const handleBlur = () => {
  console.log('编辑器失去焦点');
};

// 工具方法
const insertProcedure = () => {
  const procedureTemplate = `
:PROCEDURE NewProcedure
  :DECLARE variable1, variable2
  
  /* 在这里添加您的代码 ;
  
:ENDPROC`;
  editorRef.value?.insertText(procedureTemplate);
};

const getSelection = () => {
  const selection = editorRef.value?.getSelection();
  if (selection) {
    alert(`选中的文本: ${selection}`);
  } else {
    alert('没有选中任何文本');
  }
};

const formatCode = () => {
  // 简单的代码格式化示例
  const currentCode = editorRef.value?.getValue() || '';
  const formattedCode = currentCode
    .split('\n')
    .map((line) => line.trim())
    .join('\n');
  editorRef.value?.setValue(formattedCode);
};

const clearEditor = () => {
  editorRef.value?.setValue('');
};

const resetCode = () => {
  code.value = sampleCode;
};
</script>

<template>
  <Page>
    <div class="ssl-editor-demo">
      <Card title="SSL代码编辑器演示" class="mb-4">
        <div class="demo-controls mb-4">
          <Space wrap>
            <div class="control-item">
              <label>只读模式:</label>
              <Switch v-model:checked="readonly" />
            </div>
            <div class="control-item">
              <label>显示行号:</label>
              <Switch v-model:checked="lineNumbers" />
            </div>
            <div class="control-item">
              <label>代码折叠:</label>
              <Switch v-model:checked="foldGutter" />
            </div>
            <div class="control-item">
              <label>搜索功能:</label>
              <Switch v-model:checked="searchEnabled" />
            </div>
          </Space>
        </div>

        <div class="demo-actions mb-4">
          <Space wrap>
            <Button type="primary" @click="insertProcedure">
              插入过程模板
            </Button>
            <Button @click="getSelection"> 获取选中文本 </Button>
            <Button @click="formatCode"> 格式化代码 </Button>
            <Button @click="clearEditor"> 清空编辑器 </Button>
            <Button @click="resetCode"> 重置示例代码 </Button>
          </Space>
        </div>

        <SslCodeEditor
          ref="editorRef"
          v-model="code"
          height="500px"
          :readonly="readonly"
          :line-numbers="lineNumbers"
          :fold-gutter="foldGutter"
          :search-enabled="searchEnabled"
          :theme="theme"
          placeholder="请输入SSL代码..."
          @change="handleChange"
          @ready="handleReady"
          @focus="handleFocus"
          @blur="handleBlur"
        />

        <div class="demo-info mt-4">
          <Card size="small" title="编辑器信息">
            <p><strong>代码长度:</strong> {{ code.length }} 字符</p>
            <p><strong>行数:</strong> {{ code.split('\n').length }} 行</p>
            <p><strong>当前配置:</strong></p>
            <ul>
              <li>只读模式: {{ readonly ? '是' : '否' }}</li>
              <li>显示行号: {{ lineNumbers ? '是' : '否' }}</li>
              <li>代码折叠: {{ foldGutter ? '是' : '否' }}</li>
              <li>搜索功能: {{ searchEnabled ? '是' : '否' }}</li>
              <li>主题: {{ theme }}</li>
            </ul>
          </Card>
        </div>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
.ssl-editor-demo {
  padding: 16px;
}

.demo-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.control-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.control-item label {
  font-weight: 500;
  white-space: nowrap;
}

.demo-actions {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.demo-info ul {
  margin: 8px 0 0 16px;
}

.demo-info li {
  margin-bottom: 4px;
}
</style>

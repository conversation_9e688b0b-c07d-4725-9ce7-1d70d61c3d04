<script setup lang="ts">
import type {
  SslCodeEditorEmits,
  SslCodeEditorInstance,
  SslCodeEditorProps,
} from './types';

import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import {
  autocompletion,
  closeBrackets,
  closeBracketsKeymap,
} from '@codemirror/autocomplete';
import {
  defaultKeymap,
  history,
  historyKeymap,
  indentWithTab,
  redo,
  undo,
} from '@codemirror/commands';
import {
  bracketMatching,
  foldGutter,
  foldKeymap,
  indentOnInput,
} from '@codemirror/language';
import { search, searchKeymap } from '@codemirror/search';
import { EditorState } from '@codemirror/state';
import { oneDark } from '@codemirror/theme-one-dark';
import {
  crosshairCursor,
  drawSelection,
  dropCursor,
  EditorView,
  highlightActiveLineGutter,
  highlightSpecialChars,
  keymap,
  lineNumbers,
  rectangularSelection,
} from '@codemirror/view';

import { ssl } from './ssl-language';

// 组件属性定义
const props = withDefaults(defineProps<SslCodeEditorProps>(), {
  modelValue: '',
  height: '300px',
  width: '100%',
  readonly: false,
  placeholder: '请输入SSL代码...',
  lineNumbers: true,
  foldGutter: true,
  lineWrapping: false,
  searchEnabled: true,
  autocomplete: true,
  theme: 'auto',
  fontSize: 14,
  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
  sslValidation: false,
  sslAutocomplete: true,
  sslSnippets: false,
});

// 事件定义
const emit = defineEmits<SslCodeEditorEmits>();

// 响应式引用
const editorContainer = ref<HTMLDivElement>();
const editorView = ref<EditorView>();
const isReady = ref(false);

// 创建编辑器状态
const createEditorState = (content: string) => {
  const extensions = [
    // 基础功能
    highlightActiveLineGutter(),
    highlightSpecialChars(),
    history(),
    drawSelection(),
    dropCursor(),
    EditorState.allowMultipleSelections.of(true),
    indentOnInput(),
    bracketMatching(),
    closeBrackets(),
    rectangularSelection(),
    crosshairCursor(),

    // SSL语法支持
    ssl(),

    // 更新监听器
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        const newValue = update.state.doc.toString();
        emit('update:modelValue', newValue);
        emit('change', newValue);
      }
    }),
    EditorView.theme({
      '&': {
        fontSize: `${props.fontSize}px`,
        fontFamily: props.fontFamily,
      },
      '.cm-editor': {
        height:
          typeof props.height === 'number' ? `${props.height}px` : props.height,
        width:
          typeof props.width === 'number' ? `${props.width}px` : props.width,
      },
      '.cm-focused': {
        outline: 'none',
      },
      '.cm-scroller': {
        fontFamily: props.fontFamily,
      },
    }),
    EditorState.readOnly.of(props.readonly),
    ...(props.lineWrapping ? [EditorView.lineWrapping] : []),
  ];

  // 键盘映射
  extensions.push(
    keymap.of([
      ...closeBracketsKeymap,
      ...defaultKeymap,
      ...historyKeymap,
      ...foldKeymap,
      indentWithTab,
    ]),
  );

  // 行号
  if (props.lineNumbers) {
    extensions.push(lineNumbers());
  }

  // 添加可选扩展
  if (props.searchEnabled) {
    extensions.push(search());
    extensions.push(keymap.of(searchKeymap));
  }

  if (props.autocomplete && props.sslAutocomplete) {
    extensions.push(autocompletion());
  }

  if (props.foldGutter) {
    extensions.push(foldGutter());
  }

  // 主题支持
  if (props.theme === 'dark') {
    extensions.push(oneDark);
  }

  // 占位符支持
  if (props.placeholder) {
    extensions.push(
      EditorView.domEventHandlers({
        focus: (event) => emit('focus', event),
        blur: (event) => emit('blur', event),
      }),
    );
  }

  return EditorState.create({
    doc: content,
    extensions,
  });
};

// 初始化编辑器
const initEditor = async () => {
  if (!editorContainer.value) return;

  await nextTick();

  const state = createEditorState(props.modelValue);

  editorView.value = new EditorView({
    state,
    parent: editorContainer.value,
  });

  isReady.value = true;
  emit('ready');
};

// 更新编辑器内容
const updateEditorContent = (newValue: string) => {
  if (!editorView.value || !isReady.value) return;

  const currentValue = editorView.value.state.doc.toString();
  if (currentValue !== newValue) {
    editorView.value.dispatch({
      changes: {
        from: 0,
        to: editorView.value.state.doc.length,
        insert: newValue,
      },
    });
  }
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    updateEditorContent(newValue);
  },
);

// 组件实例方法
const focus = () => {
  editorView.value?.focus();
};

const blur = () => {
  editorView.value?.contentDOM.blur();
};

const getSelection = (): string => {
  if (!editorView.value) return '';
  const { from, to } = editorView.value.state.selection.main;
  return editorView.value.state.doc.sliceString(from, to);
};

const insertText = (text: string) => {
  if (!editorView.value) return;
  const { from } = editorView.value.state.selection.main;
  editorView.value.dispatch({
    changes: { from, insert: text },
    selection: { anchor: from + text.length },
  });
};

const replaceSelection = (text: string) => {
  if (!editorView.value) return;
  const { from, to } = editorView.value.state.selection.main;
  editorView.value.dispatch({
    changes: { from, to, insert: text },
    selection: { anchor: from + text.length },
  });
};

const undoAction = () => {
  if (!editorView.value) return;
  undo(editorView.value);
};

const redoAction = () => {
  if (!editorView.value) return;
  redo(editorView.value);
};

const getValue = (): string => {
  return editorView.value?.state.doc.toString() || '';
};

const setValue = (value: string) => {
  updateEditorContent(value);
};

// 暴露组件实例方法
defineExpose<SslCodeEditorInstance>({
  focus,
  blur,
  getSelection,
  insertText,
  replaceSelection,
  undo: undoAction,
  redo: redoAction,
  getValue,
  setValue,
});

// 生命周期
onMounted(() => {
  initEditor();
});

onUnmounted(() => {
  editorView.value?.destroy();
});
</script>

<template>
  <div class="ssl-code-editor">
    <div
      ref="editorContainer"
      class="ssl-code-editor__container"
      :class="{
        'ssl-code-editor__container--readonly': readonly,
      }"
    ></div>
  </div>
</template>

<style scoped>
.ssl-code-editor {
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.2s;
}

.ssl-code-editor:hover {
  border-color: #4096ff;
}

.ssl-code-editor:focus-within {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgb(5 145 255 / 10%);
}

.ssl-code-editor__container {
  width: 100%;
  height: 100%;
}

.ssl-code-editor__container--readonly {
  background-color: #f5f5f5;
}

/* 暗色主题支持 */
:global(.dark) .ssl-code-editor {
  background-color: #1e1e1e;
  border-color: #424242;
}

:global(.dark) .ssl-code-editor:hover {
  border-color: #4096ff;
}

:global(.dark) .ssl-code-editor__container--readonly {
  background-color: #2a2a2a;
}

/* CodeMirror样式覆盖 */
:global(.ssl-code-editor .cm-editor) {
  outline: none;
  border: none;
}

:global(.ssl-code-editor .cm-focused) {
  outline: none;
}
</style>

import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:settings',
      order: 9997,
      title: $t('system.title'),
    },
    name: 'System',
    path: '/system',
    children: [
      {
        path: '/system/sessions',
        name: 'SystemSessions',
        meta: {
          icon: 'lucide:activity',
          title: $t('system.sessions.title'),
        },
        component: () => import('#/views/system/sessions/index.vue'),
      },
      {
        path: '/system/notifications',
        name: 'Notifications',
        meta: {
          icon: 'lucide:bell',
          title: $t('system.notifications.title'),
        },
        component: () => import('#/views/system/notification/index.vue'),
      },
      {
        path: '/system/email-template',
        name: 'EmailTemplate',
        meta: {
          icon: 'lucide:mail',
          title: $t('system.emailTemplate.title'),
        },
        component: () => import('#/views/system/email-template/list.vue'),
      },
      {
        name: 'SslEditorDemo',
        path: '/examples/ssl-editor',
        component: () => import('#/views/examples/ssl-editor-demo.vue'),
        meta: {
          icon: 'lucide:code',
          title: 'SSL代码编辑器',
        },
      },
    ],
  },
];

export default routes;

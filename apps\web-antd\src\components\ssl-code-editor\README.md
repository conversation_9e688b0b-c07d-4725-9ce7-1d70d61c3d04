# SSL代码编辑器组件

基于CodeMirror 6的SSL (Starlims Scripting Language) 脚本语言编辑器组件。

## 功能特性

- ✅ SSL语法高亮显示
- ✅ 代码缩进和格式化
- ✅ 行号显示
- ✅ 代码折叠功能
- ✅ 搜索和替换
- ✅ 自动完成
- ✅ 撤销/重做
- ✅ 双向数据绑定 (v-model)
- ✅ 主题切换支持
- ✅ 只读模式
- ✅ 响应式设计

## 基本用法

```vue
<script setup lang="ts">
import { ref } from 'vue';
import { SslCodeEditor } from '#/components/ssl-code-editor';

const code = ref(`
:PROCEDURE TestProcedure
  :DECLARE sampleId
  sampleId := GetDataSet("SELECT SAMPLEID FROM SAMPLE WHERE STATUS = 'A'")
  :IF !Empty(sampleId)
    InfoMes("Sample ID: " + sampleId)
  :ENDIF
:ENDPROC
`);
</script>

<template>
  <SslCodeEditor 
    v-model="code"
    height="400px"
    :line-numbers="true"
    :fold-gutter="true"
    :search-enabled="true"
  />
</template>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `string` | `''` | 双向绑定的代码内容 |
| height | `string \| number` | `'300px'` | 编辑器高度 |
| width | `string \| number` | `'100%'` | 编辑器宽度 |
| readonly | `boolean` | `false` | 是否只读 |
| placeholder | `string` | `'请输入SSL代码...'` | 占位符文本 |
| lineNumbers | `boolean` | `true` | 是否显示行号 |
| foldGutter | `boolean` | `true` | 是否启用代码折叠 |
| lineWrapping | `boolean` | `false` | 是否启用自动换行 |
| searchEnabled | `boolean` | `true` | 是否启用搜索功能 |
| autocomplete | `boolean` | `true` | 是否启用自动完成 |
| theme | `'light' \| 'dark' \| 'auto'` | `'auto'` | 编辑器主题 |
| fontSize | `number` | `14` | 字体大小 |
| fontFamily | `string` | `'Monaco, Menlo, "Ubuntu Mono", monospace'` | 字体族 |
| sslValidation | `boolean` | `false` | 是否启用SSL语法验证 |
| sslAutocomplete | `boolean` | `true` | 是否启用SSL自动完成 |
| sslSnippets | `boolean` | `false` | 是否启用SSL代码片段 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(value: string)` | 内容变化事件 |
| change | `(value: string)` | 内容变化事件（兼容性） |
| focus | `(event: FocusEvent)` | 获得焦点事件 |
| blur | `(event: FocusEvent)` | 失去焦点事件 |
| ready | `()` | 编辑器初始化完成事件 |

### 实例方法

通过模板引用可以调用以下方法：

```vue
<script setup lang="ts">
import { ref } from 'vue';
import type { SslCodeEditorInstance } from '#/components/ssl-code-editor';

const editorRef = ref<SslCodeEditorInstance>();

const handleInsertText = () => {
  editorRef.value?.insertText(':PROCEDURE NewProc\n:ENDPROC');
};
</script>

<template>
  <SslCodeEditor ref="editorRef" v-model="code" />
  <button @click="handleInsertText">插入代码</button>
</template>
```

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | `()` | `void` | 聚焦编辑器 |
| blur | `()` | `void` | 失去焦点 |
| getSelection | `()` | `string` | 获取选中文本 |
| insertText | `(text: string)` | `void` | 插入文本 |
| replaceSelection | `(text: string)` | `void` | 替换选中文本 |
| undo | `()` | `void` | 撤销 |
| redo | `()` | `void` | 重做 |
| getValue | `()` | `string` | 获取编辑器内容 |
| setValue | `(value: string)` | `void` | 设置编辑器内容 |

## SSL语法支持

组件支持完整的SSL语法高亮，包括：

### 控制关键字
- `:BEGINCASE`, `:CASE`, `:ENDCASE`, `:IF`, `:ENDIF`
- `:WHILE`, `:ENDWHILE`, `:FOR`, `:NEXT`
- `:TRY`, `:CATCH`, `:FINALLY`, `:ENDTRY`

### 存储关键字
- `:DECLARE`, `:PUBLIC`, `:PROCEDURE`, `:ENDPROC`
- `:CLASS`, `:INHERIT`, `:INCLUDE`

### 内置函数
- `GetDataSet`, `SqlExecute`, `DoProc`, `ExecFunction`
- `LimsDate`, `LimsTime`, `InfoMes`, `ErrorMes`
- 以及更多SSL内置函数...

### 导航变量
- `MYUSERNAME`, `STARLIMSDEPT`, `MYUSERROLE`

## 主题定制

组件支持明暗主题切换，并与项目整体主题保持一致：

```vue
<template>
  <!-- 自动跟随系统主题 -->
  <SslCodeEditor v-model="code" theme="auto" />
  
  <!-- 强制使用暗色主题 -->
  <SslCodeEditor v-model="code" theme="dark" />
  
  <!-- 强制使用亮色主题 -->
  <SslCodeEditor v-model="code" theme="light" />
</template>
```

## 注意事项

1. 组件基于CodeMirror 6构建，需要现代浏览器支持
2. 大文件编辑时建议启用虚拟滚动以提升性能
3. SSL语法验证功能需要额外配置（当前版本暂未实现）
4. 代码片段功能需要额外配置（当前版本暂未实现）

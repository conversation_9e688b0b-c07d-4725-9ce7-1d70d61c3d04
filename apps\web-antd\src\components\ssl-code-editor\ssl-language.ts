/**
 * SSL (Starlims Scripting Language) 语法定义
 * 基于 CodeMirror 6 的语法高亮支持
 */

import { LanguageSupport, StreamLanguage } from '@codemirror/language';

/**
 * SSL控制关键字
 */
const SSL_CONTROL_KEYWORDS = [
  'BEGINCASE',
  'CASE',
  'ENDCASE',
  'EXITCASE',
  'OTHERWISE',
  'IF',
  'DEFAULT',
  'REGION',
  'ENDREGI<PERSON>',
  'ENDIF',
  'ELSE',
  'LOOP',
  'WHILE',
  'ENDWHILE',
  'FOR',
  'NEXT',
  'STEP',
  'EXITFOR',
  'RESUME',
  'EXITCASE',
  'RETURN',
  'TRY',
  'CATCH',
  'FINALLY',
  'ENDTRY',
  'TO',
];

/**
 * SSL存储关键字
 */
const SSL_STORAGE_KEYWORDS = [
  'PUBLIC',
  'DECLARE',
  'CLASS',
  'INHERIT',
  'INCLUDE',
  'PARAMETERS',
  'PROCEDURE',
  'ENDPR<PERSON>',
  'REGION',
  'ENDREGI<PERSON>',
  'B<PERSON><PERSON><PERSON>LINECOD<PERSON>',
  '<PERSON><PERSON><PERSON>LINECODE',
  'ERROR',
];

/**
 * SSL内置函数（部分常用函数）
 */
const SSL_BUILTIN_FUNCTIONS = [
  'DoProc',
  'ExecFunction',
  'ExecUdf',
  'CreateUDObject',
  'Branch',
  'GetDataSet',
  'GetDataSetEx',
  'SqlExecute',
  'RunSql',
  'LSearch',
  'LSelect',
  'GetUserData',
  'SetUserData',
  'ValidateData',
  'EncryptData',
  'DecryptData',
  'LimsDate',
  'LimsTime',
  'Now',
  'Today',
  'DateAdd',
  'DateDiff',
  'Left',
  'Right',
  'SubStr',
  'Len',
  'Upper',
  'Lower',
  'Trim',
  'LTrim',
  'Str',
  'Val',
  'IsNumeric',
  'Empty',
  'IsDefined',
  'SendLimsEmail',
  'SendToOutbox',
  'SendFromOutbox',
  'FileSupport',
  'Directory',
  'ReadText',
  'WriteText',
  'CreateGUID',
  'GetSetting',
  'SetSetting',
  'InfoMes',
  'ErrorMes',
];

/**
 * SSL导航变量
 */
const SSL_NAVIGATOR_VARIABLES = ['MYUSERNAME', 'STARLIMSDEPT', 'MYUSERROLE'];

/**
 * SSL语言常量
 */
const SSL_LANGUAGE_CONSTANTS = ['.t.', '.T.', '.f.', '.F.'];

/**
 * SSL操作符
 */
const SSL_OPERATORS = [
  '=',
  ':=',
  '>=',
  '<=',
  '>',
  '<',
  '==',
  '!=',
  '+',
  '-',
  '*',
  '/',
  '.and.',
  '.AND.',
  '.or.',
  '.OR.',
];

/**
 * SSL语法解析器
 */
const sslParser = {
  name: 'ssl',

  token(stream: any, state: any) {
    // 跳过空白字符
    if (stream.eatSpace()) return null;

    // 注释处理 /* ... ;
    if (stream.match('/*')) {
      state.inComment = true;
      return 'comment';
    }
    if (state.inComment) {
      if (stream.match(';')) {
        state.inComment = false;
        return 'comment';
      }
      stream.next();
      return 'comment';
    }

    // 字符串处理
    if (/^"([^"\\]|\\.)*"/.test(stream)) return 'string';
    if (/^'([^'\\]|\\.)*'/.test(stream)) return 'string';

    // 数字处理
    if (/^-?\d+(\.\d+)?/.test(stream)) return 'number';

    // 语言常量
    if (/^\.([tf])\./i.test(stream)) return 'atom';

    // 控制关键字
    const controlKeywordPattern = new RegExp(
      `^:(${SSL_CONTROL_KEYWORDS.join('|')})\\b`,
      'i',
    );
    if (controlKeywordPattern.test(stream)) return 'keyword';

    // 存储关键字
    const storageKeywordPattern = new RegExp(
      `^:(${SSL_STORAGE_KEYWORDS.join('|')})\\b`,
      'i',
    );
    if (storageKeywordPattern.test(stream)) return 'def';

    // 内置函数
    const functionPattern = new RegExp(
      `^(${SSL_BUILTIN_FUNCTIONS.join('|')})\\b`,
      'i',
    );
    if (functionPattern.test(stream)) return 'builtin';

    // 导航变量
    const navVarPattern = new RegExp(
      `^(${SSL_NAVIGATOR_VARIABLES.join('|')})\\b`,
      'i',
    );
    if (navVarPattern.test(stream)) return 'variable-2';

    // 操作符
    if (/^(\.(and|AND|or|OR)\.)|^([=+\-]|:=|>=?|<=?|==|!=)/.test(stream))
      return 'operator';

    // 标识符
    if (/^[a-z_]\w*/i.test(stream)) return 'variable';

    // 其他字符
    stream.next();
    return null;
  },

  startState() {
    return {
      inComment: false,
    };
  },

  languageData: {
    commentTokens: { block: { open: '/*', close: ';' } },
    indentOnInput: /^\s*[}\])]$/,
    closeBrackets: { brackets: ['(', '[', '{', '"', "'"] },
  },
};

/**
 * 创建SSL语言支持
 */
export function ssl(): LanguageSupport {
  return new LanguageSupport(StreamLanguage.define(sslParser));
}

/**
 * 导出SSL语法配置
 */
export const sslSyntaxConfig = {
  controlKeywords: SSL_CONTROL_KEYWORDS,
  storageKeywords: SSL_STORAGE_KEYWORDS,
  builtinFunctions: SSL_BUILTIN_FUNCTIONS,
  navigatorVariables: SSL_NAVIGATOR_VARIABLES,
  languageConstants: SSL_LANGUAGE_CONSTANTS,
  operators: SSL_OPERATORS,
};

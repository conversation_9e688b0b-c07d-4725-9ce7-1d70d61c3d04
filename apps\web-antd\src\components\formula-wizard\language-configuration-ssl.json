{"comments": {"blockComment": ["/*", ";"], "lineComment": "/*"}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], {"open": "/*", "close": ";", "notIn": ["string"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"]], "folding": {"offSide": false, "markers": {"start": "^\\s*(\\/\\*\\s?region).*$", "end": "^\\s*(\\/\\*endregion;).*$"}, "indentation": "spaces", "openByDefault": false}, "onEnterRules": [{"beforeText": "^\\s*(:FOR|:WHILE|:PROCEDURE|:IF|:BEGINCASE|:CASE|:TRY|:CATCH|:FINALLY|:REGION).*$", "action": {"indent": "indent"}}, {"beforeText": "^\\s*(:ENDCASE|:END|:NEXT|:ENDWHILE|:ENDPROCEDURE|:ENDIF|:ENDTRY|:ENDREGION).*$", "action": {"indent": "outdent"}}], "wordPattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)"}
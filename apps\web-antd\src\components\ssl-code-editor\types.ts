/**
 * SSL代码编辑器组件类型定义
 */

/**
 * 编辑器主题类型
 */
export type SslCodeEditorTheme = 'light' | 'dark' | 'auto';

/**
 * SSL代码编辑器配置选项
 */
export interface SslCodeEditorOptions {
  /** 编辑器高度 */
  height?: string | number;
  /** 编辑器宽度 */
  width?: string | number;
  /** 是否只读 */
  readonly?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示行号 */
  lineNumbers?: boolean;
  /** 是否启用代码折叠 */
  foldGutter?: boolean;
  /** 是否启用自动换行 */
  lineWrapping?: boolean;
  /** 是否启用搜索功能 */
  searchEnabled?: boolean;
  /** 是否启用自动完成 */
  autocomplete?: boolean;
  /** 编辑器主题 */
  theme?: SslCodeEditorTheme;
  /** 字体大小 */
  fontSize?: number;
  /** 字体族 */
  fontFamily?: string;
  /** 是否启用SSL语法验证 */
  sslValidation?: boolean;
  /** 是否启用SSL自动完成 */
  sslAutocomplete?: boolean;
  /** 是否启用SSL代码片段 */
  sslSnippets?: boolean;
}

/**
 * SSL代码编辑器组件Props
 */
export interface SslCodeEditorProps extends Partial<SslCodeEditorOptions> {
  /** 双向绑定的代码内容 */
  modelValue?: string;
}

/**
 * SSL代码编辑器事件
 */
export interface SslCodeEditorEmits {
  /** 内容变化事件 */
  'update:modelValue': [value: string];
  /** 内容变化事件（兼容性） */
  'change': [value: string];
  /** 获得焦点事件 */
  'focus': [event: FocusEvent];
  /** 失去焦点事件 */
  'blur': [event: FocusEvent];
  /** 编辑器初始化完成事件 */
  'ready': [];
}

/**
 * 编辑器实例方法
 */
export interface SslCodeEditorInstance {
  /** 聚焦编辑器 */
  focus(): void;
  /** 失去焦点 */
  blur(): void;
  /** 获取选中文本 */
  getSelection(): string;
  /** 插入文本 */
  insertText(text: string): void;
  /** 替换选中文本 */
  replaceSelection(text: string): void;
  /** 撤销 */
  undo(): void;
  /** 重做 */
  redo(): void;
  /** 获取编辑器内容 */
  getValue(): string;
  /** 设置编辑器内容 */
  setValue(value: string): void;
}

/**
 * SSL语法元素类型
 */
export interface SslSyntaxElement {
  /** 元素类型 */
  type: 'keyword' | 'function' | 'variable' | 'string' | 'comment' | 'number' | 'operator';
  /** 元素值 */
  value: string;
  /** 样式类名 */
  className?: string;
}

/**
 * SSL语法高亮配置
 */
export interface SslHighlightConfig {
  /** 控制关键字 */
  controlKeywords: string[];
  /** 存储关键字 */
  storageKeywords: string[];
  /** 内置函数 */
  builtinFunctions: string[];
  /** 导航变量 */
  navigatorVariables: string[];
  /** 语言常量 */
  languageConstants: string[];
  /** 操作符 */
  operators: string[];
}
